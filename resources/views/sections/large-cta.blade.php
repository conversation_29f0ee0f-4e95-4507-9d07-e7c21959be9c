@php
    $tagline = $attributes['tagline'] ?? '';
    $title = $attributes['title'] ?? '';
    $description = $attributes['description'] ?? '';
    $benefits = $attributes['benefits'] ?? [];
    $buttonText = $attributes['buttonText'] ?? '';
    $buttonLink = $attributes['buttonLink'] ?? '#';
@endphp

<div class="large-cta-section relative flex justify-center items-center mb-8">
    <div class="container lg:max-w-[856px] lg:m-auto py-8 lg:py-[62px]">
        <div class="block w-full p-12 flex flex-col justify-between items-start relative overflow-hidden lg:rounded-3xl" style="background: linear-gradient(to right, #bcf4df, #d4f4f0);">
            {{-- Noise texture overlay --}}
            <div class="absolute inset-0 z-0 opacity-20 bg-repeat" style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

            <div class="relative z-10 w-full">
                @if(!empty($tagline))
                    <x-title variation="tagline" class="uppercase text-center !text-[#538564] mb-2">{!! $tagline !!}</x-title>
                @endif

                @if(!empty($title))
                    <x-title variation="medium" class="text-center mb-2">{!! $title !!}</x-title>
                @endif

                @if(!empty($description))
                    <p class="text-bodySmall lg:text-bodySmallDesktop max-w-md mx-auto text-center mb-7">{!! $description !!}</p>
                @endif

                @if(!empty($benefits) && count($benefits) > 0)
                    <div class="flex flex-wrap max-w-md mx-auto justify-center gap-4 mb-8">
                        @foreach($benefits as $benefit)
                            @if(!empty($benefit['text']))
                                <div class="flex items-start gap-2">
                                    <div class="flex-shrink-0 w-5 h-5 bg-[#E8F8ED] rounded-full flex items-center justify-center">
                                        <i class="fa-regular text-xs text-[#538564] fa-check"></i>
                                    </div>
                                    <p class="text-bodySmall text-[#3F4A5A] pt-0.5 font-normal lg:text-bodySmallDesktop">{!! $benefit['text'] !!}</p>
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif

                @if(!empty($buttonText) && !empty($buttonLink))
                    <div class="flex justify-center">
                        <x-button
                            type="link"
                            :href="$buttonLink"
                            target="_blank"
                            rel="noopener noreferrer"
                            size="medium"
                            variant="primary">
                            {{ $buttonText }}
                        </x-button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
