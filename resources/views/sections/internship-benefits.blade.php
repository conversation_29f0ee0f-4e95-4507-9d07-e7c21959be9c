@php
    $variant = $attributes['variant'] ?? 'default';
@endphp

@if($variant === 'line')
    <div class="internship-benefits-section internship-benefits-line flex justify-center items-center">
        <div class="container lg:max-w-[856px] lg:m-auto px-5 py-8 lg:py-[62px]">
            <div class="text-center mb-12">
                @if(!empty($attributes['title']))
                    <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="max-w-[600px] mx-auto">{!! $attributes['description'] !!}</p>
                @endif
            </div>

            @if(!empty($attributes['benefits']) && count($attributes['benefits']) > 0)
                <div class="benefits-line-container flex flex-col lg:flex-row lg:items-start lg:gap-16">
                    <div class="lg:w-1/2 mb-8 lg:mb-0">
                        <div class="benefits-progress flex flex-col gap-6">
                            @foreach($attributes['benefits'] as $index => $benefit)
                                <div class="benefit-progress-item flex items-center gap-4 cursor-pointer" data-benefit-index="{{ $index }}">
                                    <div class="icon-container relative w-12 h-12 flex-shrink-0">
                                        @if(!empty($benefit['icon']))
                                            <img src="{{ $benefit['icon'] }}" alt="" class="w-8 h-8 object-contain mx-auto">
                                        @endif
                                    </div>
                                    <div class="progress-line-container flex-1">
                                        <div class="progress-line w-full h-1 bg-[#F4F6FA] rounded-full overflow-hidden">
                                            <div class="progress-fill h-full bg-[#CCEFFF] rounded-full transition-all duration-1000 ease-out" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="lg:w-1/2">
                        <div class="benefits-content relative min-h-[120px]">
                            @foreach($attributes['benefits'] as $index => $benefit)
                                <div class="benefit-content-item absolute inset-0 transition-all duration-500 ease-in-out {{ $index === 0 ? 'opacity-100 visible' : 'opacity-0 invisible' }}" data-benefit-content="{{ $index }}">
                                    @if(!empty($benefit['title']))
                                        <h3 class="text-xl font-semibold mb-3 lg:text-2xl">{!! $benefit['title'] !!}</h3>
                                    @endif
                                    @if(!empty($benefit['description']))
                                        <p class="text-[#566276] leading-relaxed">{!! $benefit['description'] !!}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@else
    <div class="internship-benefits-section flex justify-center items-center">
        <div class="container px-5 py-8 lg:py-[62px]">
            <div class="flex flex-col lg:flex-row lg:gap-16">
                <div class="lg:w-1/2 mb-8 lg:mb-0">
                    @if(!empty($attributes['title']))
                        <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
                    @endif
                    @if(!empty($attributes['description']))
                        <p class="lg:max-w-[470px]">{!! $attributes['description'] !!}</p>
                    @endif
                </div>

                @if(!empty($attributes['benefits']) && count($attributes['benefits']) > 0)
                    <div class="lg:w-1/2 flex flex-col gap-3 lg:max-w-[416px]">
                        @foreach($attributes['benefits'] as $benefit)
                            <div class="benefit-item border border-solid border-[#E8EBF3] rounded-[20px] p-4 flex gap-4">
                                <div class="icon-container relative w-9 h-9 flex-shrink-0">
                                    <div class="absolute inset-0 rounded-[8px]" style="
                                        background: linear-gradient(54.97deg, #CCEFFF -1.61%, #CCEFFF 53.98%, #D7F4E1 100%);
                                        padding: 3px;
                                        z-index: 1;
                                    ">
                                        <div class="w-full h-full bg-white/80 rounded-md flex items-center justify-center">
                                            @if(!empty($benefit['icon']))
                                                <img src="{{ $benefit['icon'] }}" alt="" class="w-6 h-6 object-contain">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="benefit-content">
                                    @if(!empty($benefit['title']))
                                        <span class="block text-bodyMedium font-normal lg:text-bodyMediumDesktop">{!! $benefit['title'] !!}</span>
                                    @endif
                                    @if(!empty($benefit['description']))
                                        <p class="text-bodySmall lg:text-bodySmallDesktop text-[#566276]">{!! $benefit['description'] !!}</p>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
@endif
