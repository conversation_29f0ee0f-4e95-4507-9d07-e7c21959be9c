<div class="big-banner-section flex justify-center items-center">
    <div class="container px-5 py-8 lg:pt-[124px] lg:pb-[62px]">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-8">
            <div class="lg:w-1/2 lg:pr-12 mb-6 lg:mb-0">
                @if(!empty($attributes['title']))
                    <x-title variation="large">{!! $attributes['title'] !!}</x-title>
                @endif
            </div>
            <div class="lg:w-1/2">
                @if(!empty($attributes['description']))
                    <p>{!! $attributes['description'] !!}</p>
                @endif
            </div>
        </div>

        @if(!empty($attributes['stats']) && count($attributes['stats']) > 0)
            <div class="stats-section flex flex-col justify-between border-t border-[#E8EBF3] pt-8 my-8 lg:flex-row lg:items-end lg:pt-16 lg:my-16">
                @if(!empty($attributes['statsTitle']))
                    <span class="text-bodyMedium mb-8 lg:mb-0 lg:text-bodyMediumDesktop">{!! $attributes['statsTitle'] !!}</span>
                @endif
                <div class="flex gap-16 justify-start">
                    @foreach($attributes['stats'] as $stat)
                        <div class="flex flex-col items-center">
                            @if(!empty($stat['value']))
                                <div class="text-title lg:text-titleDesktop">{!! $stat['value'] !!}</div>
                            @endif
                            @if(!empty($stat['label']))
                                <div class="text-bodySmall mb-0.5 lg:text-bodySmallDesktop">{!! $stat['label'] !!}</div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if(!empty($attributes['mainImage']))
            <img src="{{ $attributes['mainImage'] }}" alt="Banner Image" class="w-full h-auto rounded-3xl object-cover">
        @endif

        @if(!empty($attributes['pills']) && count($attributes['pills']) > 0)
            <div class="flex flex-wrap justify-center gap-4 lg:justify-start mb-8">
                @foreach($attributes['pills'] as $pill)
                    <div class="flex items-center py-[10px] px-[12px] bg-[#F4F6FA] text-[#00567F] rounded-lg">
                        @if(!empty($pill['image']))
                            <img src="{{ $pill['image'] }}" alt="" class="w-5 h-5 object-contain mr-2">
                        @endif
                        <span class="text-bodySmall lg:text-bodySmallDesktop font-normal pt-0.5 block">{!! $pill['text'] !!}</span>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</div>
