import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ButtonEditor,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/large-cta', {
    apiVersion: 2,
    title: 'Large CTA',
    icon: 'megaphone',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        benefits: {
            type: 'array',
            default: [
                {
                    text: '',
                }
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { tagline, title, description, benefits, buttonText, buttonLink } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        // Handle benefit change
        const handleBenefitChange = (index, field, value) => {
            const updatedBenefits = [...benefits];
            updatedBenefits[index][field] = value;
            setAttributes({ benefits: updatedBenefits });
        };

        // Add a new benefit
        const addBenefit = () => {
            const newBenefits = [...benefits, { text: '' }];
            setAttributes({ benefits: newBenefits });
            setActiveTab(newBenefits.length - 1);
        };

        // Remove a benefit
        const removeBenefit = (index) => {
            const updatedBenefits = [...benefits];
            updatedBenefits.splice(index, 1);
            setAttributes({ benefits: updatedBenefits });
            setActiveTab(Math.min(activeTab, updatedBenefits.length - 1));
        };

        return (
            <div {...blockProps}>
                <EditorContainer title="Large CTA" icon="megaphone">
                    <div className="mb-6">
                        <TextControl
                            label="Tagline"
                            value={tagline}
                            onChange={(value) => setAttributes({ tagline: value })}
                            placeholder="Enter tagline"
                            tagName="h5"
                        />
                    </div>

                    <div className="mb-6">
                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />
                    </div>

                    <div className="mb-6">
                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter description"
                            tagName="p"
                            multiline={true}
                        />
                    </div>

                    <div className="mb-6">
                        <TabNavigation
                            items={benefits}
                            activeTab={activeTab}
                            onTabChange={setActiveTab}
                            onAddItem={addBenefit}
                            onRemoveItem={() => removeBenefit(activeTab)}
                            getItemTitle={(item, index) => item.text && item.text.trim().length > 0 ? item.text : `Benefit ${index + 1}`}
                            itemName="Benefit"
                            addButtonTitle="Add New Benefit"
                        />

                        {benefits.length > 0 && (
                            <ItemEditor
                                itemIndex={activeTab}
                                onRemove={() => removeBenefit(activeTab)}
                                itemName="Benefit"
                                showRemoveButton={benefits.length > 1}
                            >
                                <TextControl
                                    label="Benefit Text"
                                    value={benefits[activeTab].text}
                                    onChange={(value) => handleBenefitChange(activeTab, 'text', value)}
                                    placeholder="Enter benefit text"
                                    tagName="p"
                                />
                            </ItemEditor>
                        )}
                    </div>

                    <div className="mb-6">
                        <ButtonEditor
                            buttonText={buttonText}
                            buttonLink={buttonLink}
                            onChangeText={(value) => setAttributes({ buttonText: value })}
                            onChangeLink={(value) => setAttributes({ buttonLink: value })}
                            label="CTA Button"
                        />
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
