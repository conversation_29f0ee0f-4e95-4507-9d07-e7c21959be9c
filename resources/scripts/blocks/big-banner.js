import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/big-banner', {
    apiVersion: 2,
    title: 'Big Banner',
    icon: 'format-image',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        mainImage: {
            type: 'string',
            default: '',
        },
        pills: {
            type: 'array',
            default: [],
        },
        statsTitle: {
            type: 'string',
            default: '',
        },
        stats: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, mainImage, pills, statsTitle, stats } = attributes;
        const [activePill, setActivePill] = useState(0);
        const [activeStat, setActiveStat] = useState(0);

        // Add a new pill item
        const addPill = () => {
            const newPill = {
                image: '',
                text: '',
            };
            const newPills = [...pills, newPill];
            setAttributes({ pills: newPills });
            setActivePill(newPills.length - 1);
        };

        // Handle pill item changes
        const handlePillChange = (index, field, value) => {
            const updatedPills = [...pills];
            updatedPills[index] = { ...updatedPills[index], [field]: value };
            setAttributes({ pills: updatedPills });
        };

        // Remove a pill item
        const removePill = (index) => {
            const updatedPills = [...pills];
            updatedPills.splice(index, 1);
            setAttributes({ pills: updatedPills });
            setActivePill(Math.min(index, updatedPills.length - 1));
        };

        // Add a new stat item
        const addStat = () => {
            if (stats.length >= 3) return; // Max 3 stats
            const newStat = {
                label: '',
                value: '',
            };
            const newStats = [...stats, newStat];
            setAttributes({ stats: newStats });
            setActiveStat(newStats.length - 1);
        };

        // Handle stat item changes
        const handleStatChange = (index, field, value) => {
            const updatedStats = [...stats];
            updatedStats[index] = { ...updatedStats[index], [field]: value };
            setAttributes({ stats: updatedStats });
        };

        // Remove a stat item
        const removeStat = (index) => {
            const updatedStats = [...stats];
            updatedStats.splice(index, 1);
            setAttributes({ stats: updatedStats });
            setActiveStat(Math.min(index, updatedStats.length - 1));
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Big Banner">
                    <div className="flex flex-col lg:flex-row gap-6 mb-6">
                        <div className="lg:w-1/2">
                            <TextControl
                                label="Title"
                                value={title}
                                onChange={(value) => setAttributes({ title: value })}
                                placeholder="Enter title"
                                tagName="h2"
                            />
                        </div>
                        <div className="lg:w-1/2">
                            <TextControl
                                label="Description"
                                value={description}
                                onChange={(value) => setAttributes({ description: value })}
                                placeholder="Enter description"
                                tagName="p"
                                multiline={true}
                            />
                        </div>
                    </div>

                    <ImageUploader
                        image={mainImage}
                        onSelect={(url) => setAttributes({ mainImage: url })}
                        onRemove={() => setAttributes({ mainImage: '' })}
                        label="Main Image"
                        description="Upload the main banner image."
                        height={64}
                        objectFit="cover"
                        altText="Banner Image"
                    />

                    <div className="mt-6">
                        <TabNavigation
                            items={pills}
                            activeTab={activePill}
                            onTabChange={setActivePill}
                            onAddItem={addPill}
                            onRemoveItem={() => removePill(activePill)}
                            getItemTitle={(item, index) => item.text || `Pill ${index + 1}`}
                            itemName="Pill"
                            addButtonTitle="Add New Pill"
                        />

                        {pills.length > 0 && (
                            <ItemEditor
                                itemIndex={activePill}
                                onRemove={() => removePill(activePill)}
                                itemName="Pill"
                                showRemoveButton={true}
                            >
                                <ImageUploader
                                    image={pills[activePill].image}
                                    onSelect={(url) => handlePillChange(activePill, 'image', url)}
                                    onRemove={() => handlePillChange(activePill, 'image', '')}
                                    label="Pill Icon"
                                    description="Upload a small icon (20x20px recommended)."
                                    objectFit="contain"
                                    altText="Pill Icon"
                                />

                                <TextControl
                                    label="Pill Text"
                                    value={pills[activePill].text}
                                    onChange={(value) => handlePillChange(activePill, 'text', value)}
                                    placeholder="Enter pill text"
                                    tagName="p"
                                />
                            </ItemEditor>
                        )}
                    </div>

                    <div className="mt-6">
                        <TextControl
                            label="Stats Title"
                            value={statsTitle}
                            onChange={(value) => setAttributes({ statsTitle: value })}
                            placeholder="Enter stats section title"
                            tagName="h3"
                        />

                        <TabNavigation
                            items={stats}
                            activeTab={activeStat}
                            onTabChange={setActiveStat}
                            onAddItem={addStat}
                            onRemoveItem={() => removeStat(activeStat)}
                            getItemTitle={(item, index) => item.label || `Stat ${index + 1}`}
                            itemName="Stat"
                            addButtonTitle="Add New Stat"
                            maxItems={3}
                        />

                        {stats.length > 0 && (
                            <ItemEditor
                                itemIndex={activeStat}
                                onRemove={() => removeStat(activeStat)}
                                itemName="Stat"
                                showRemoveButton={true}
                            >
                                <TextControl
                                    label="Stat Value"
                                    value={stats[activeStat].value}
                                    onChange={(value) => handleStatChange(activeStat, 'value', value)}
                                    placeholder="Enter stat value"
                                    tagName="p"
                                />

                                <TextControl
                                    label="Stat Label"
                                    value={stats[activeStat].label}
                                    onChange={(value) => handleStatChange(activeStat, 'label', value)}
                                    placeholder="Enter stat label"
                                    tagName="p"
                                />
                            </ItemEditor>
                        )}
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
