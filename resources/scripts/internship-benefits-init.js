document.addEventListener('DOMContentLoaded', function() {
    const benefitsLineSections = document.querySelectorAll('.internship-benefits-line');

    benefitsLineSections.forEach(section => {
        const benefitItems = section.querySelectorAll('.benefit-line-item');
        const descriptionItems = section.querySelectorAll('.benefit-description');

        if (!benefitItems.length || !descriptionItems.length) return;

        let currentIndex = 0;
        let autoProgressTimer = null;
        let isAutoProgressing = true;

        // Initialize first item
        showBenefit(0);

        // Auto-progression functionality
        function startAutoProgress() {
            if (!isAutoProgressing) return;

            autoProgressTimer = setTimeout(() => {
                const nextIndex = (currentIndex + 1) % benefitItems.length;
                showBenefit(nextIndex);
                startAutoProgress(); // Continue the cycle
            }, 3000); // 3 seconds per benefit
        }

        function stopAutoProgress() {
            if (autoProgressTimer) {
                clearTimeout(autoProgressTimer);
                autoProgressTimer = null;
            }
        }

        function showBenefit(index) {
            // Update current index
            currentIndex = index;

            // Update description visibility
            descriptionItems.forEach((item, i) => {
                if (i === index) {
                    item.classList.remove('hidden');
                    item.classList.add('block');
                } else {
                    item.classList.add('hidden');
                    item.classList.remove('block');
                }
            });

            // Update vertical progress lines
            benefitItems.forEach((item, i) => {
                const progressFill = item.querySelector('.progress-fill-vertical');
                if (progressFill) {
                    if (i <= index) {
                        // Fill completed and current items (top to bottom)
                        progressFill.style.height = '100%';
                    } else {
                        // Reset future items
                        progressFill.style.height = '0%';
                    }
                }
            });
        }

        // Click handlers for manual navigation
        benefitItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                // Stop auto-progression when user clicks
                isAutoProgressing = false;
                stopAutoProgress();
                showBenefit(index);

                // Resume auto-progression after 5 seconds of inactivity
                setTimeout(() => {
                    isAutoProgressing = true;
                    startAutoProgress();
                }, 5000);
            });
        });

        // Start auto-progression
        startAutoProgress();

        // Pause auto-progression when user hovers over the section
        section.addEventListener('mouseenter', () => {
            stopAutoProgress();
        });

        section.addEventListener('mouseleave', () => {
            if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoProgress();
            } else if (isAutoProgressing) {
                startAutoProgress();
            }
        });
    });
});
