document.addEventListener('DOMContentLoaded', function() {
    const benefitsLineSections = document.querySelectorAll('.internship-benefits-line');

    benefitsLineSections.forEach(section => {
        const progressItems = section.querySelectorAll('.benefit-progress-item');
        const contentItems = section.querySelectorAll('.benefit-content-item');

        if (!progressItems.length || !contentItems.length) return;

        let currentIndex = 0;
        let autoProgressTimer = null;
        let isAutoProgressing = true;

        // Initialize first item
        showBenefit(0);

        // Auto-progression functionality
        function startAutoProgress() {
            if (!isAutoProgressing) return;

            autoProgressTimer = setTimeout(() => {
                const nextIndex = (currentIndex + 1) % progressItems.length;
                showBenefit(nextIndex);
                startAutoProgress(); // Continue the cycle
            }, 3000); // 3 seconds per benefit
        }

        function stopAutoProgress() {
            if (autoProgressTimer) {
                clearTimeout(autoProgressTimer);
                autoProgressTimer = null;
            }
        }

        function showBenefit(index) {
            // Update current index
            currentIndex = index;

            // Update content visibility with smooth transitions
            contentItems.forEach((item, i) => {
                if (i === index) {
                    item.classList.remove('opacity-0', 'invisible');
                    item.classList.add('opacity-100', 'visible');
                } else {
                    item.classList.add('opacity-0', 'invisible');
                    item.classList.remove('opacity-100', 'visible');
                }
            });

            // Update progress lines
            progressItems.forEach((item, i) => {
                const progressFill = item.querySelector('.progress-fill');
                if (i <= index) {
                    // Fill completed and current items
                    progressFill.style.width = '100%';
                } else {
                    // Reset future items
                    progressFill.style.width = '0%';
                }
            });
        }

        // Click handlers for manual navigation
        progressItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                // Stop auto-progression when user clicks
                isAutoProgressing = false;
                stopAutoProgress();
                showBenefit(index);

                // Resume auto-progression after 5 seconds of inactivity
                setTimeout(() => {
                    isAutoProgressing = true;
                    startAutoProgress();
                }, 5000);
            });
        });

        // Start auto-progression
        startAutoProgress();

        // Pause auto-progression when user hovers over the section
        section.addEventListener('mouseenter', () => {
            stopAutoProgress();
        });

        section.addEventListener('mouseleave', () => {
            if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoProgress();
            } else if (isAutoProgressing) {
                startAutoProgress();
            }
        });
    });
});
