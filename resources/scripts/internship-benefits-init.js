document.addEventListener('DOMContentLoaded', function() {
    const benefitsLineSections = document.querySelectorAll('.internship-benefits-line');

    benefitsLineSections.forEach(section => {
        const benefitItems = section.querySelectorAll('.benefit-line-item');
        const descriptionContainers = section.querySelectorAll('.benefit-description-container');
        const progressLines = section.querySelectorAll('.benefit-line');

        if (!benefitItems.length || !descriptionContainers.length) return;

        let currentIndex = 0;
        let autoProgressTimer = null;
        let isAutoProgressing = true;
        let isTransitioning = false;

        // Calculate and set line heights based on content
        function updateLineHeights() {
            benefitItems.forEach((item, index) => {
                const line = item.querySelector('.benefit-line');
                if (line && index < benefitItems.length - 1) {
                    const currentItemRect = item.getBoundingClientRect();
                    const nextItem = benefitItems[index + 1];
                    const nextItemRect = nextItem.getBoundingClientRect();
                    const lineHeight = nextItemRect.top - currentItemRect.top - 48; // 48px = icon height + some spacing
                    line.style.height = Math.max(lineHeight, 20) + 'px';
                }
            });
        }

        // Initialize line heights and first item
        updateLineHeights();
        showBenefit(0, false); // false = no animation on initial load

        // Auto-progression functionality
        function startAutoProgress() {
            if (!isAutoProgressing || isTransitioning) return;

            // Start filling the current line
            const currentLine = benefitItems[currentIndex]?.querySelector('.progress-fill-vertical');
            if (currentLine) {
                currentLine.style.height = '100%';

                // After line fills, transition to next benefit
                setTimeout(() => {
                    if (isAutoProgressing && !isTransitioning) {
                        const nextIndex = (currentIndex + 1) % benefitItems.length;
                        showBenefit(nextIndex, true);

                        // Continue the cycle after transition completes
                        setTimeout(() => {
                            startAutoProgress();
                        }, 1000); // Wait for transition to complete
                    }
                }, 2000); // 2 seconds for line to fill
            }
        }

        function stopAutoProgress() {
            if (autoProgressTimer) {
                clearTimeout(autoProgressTimer);
                autoProgressTimer = null;
            }
        }

        function showBenefit(index, animate = true) {
            if (animate) {
                isTransitioning = true;
            }

            // Update current index
            currentIndex = index;

            // Update description visibility with smooth max-height transitions
            descriptionContainers.forEach((container, i) => {
                if (i === index) {
                    container.classList.remove('max-h-0');
                    container.classList.add('max-h-96');
                } else {
                    container.classList.add('max-h-0');
                    container.classList.remove('max-h-96');
                }
            });

            // Update vertical progress lines
            benefitItems.forEach((item, i) => {
                const progressFill = item.querySelector('.progress-fill-vertical');
                if (progressFill) {
                    if (i < index) {
                        // Fill completed items
                        progressFill.style.height = '100%';
                    } else if (i === index && !animate) {
                        // Current item on initial load - don't fill yet
                        progressFill.style.height = '0%';
                    } else {
                        // Reset future items and current item during transitions
                        progressFill.style.height = '0%';
                    }
                }
            });

            if (animate) {
                // Reset transition flag after animations complete
                setTimeout(() => {
                    isTransitioning = false;
                    // Recalculate line heights after content changes
                    updateLineHeights();
                }, 500);
            }
        }

        // Click handlers for manual navigation
        benefitItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                if (isTransitioning) return; // Prevent clicks during transitions

                // Stop auto-progression when user clicks
                isAutoProgressing = false;
                stopAutoProgress();
                showBenefit(index, true);

                // Resume auto-progression after 5 seconds of inactivity
                setTimeout(() => {
                    isAutoProgressing = true;
                    startAutoProgress();
                }, 5000);
            });
        });

        // Start auto-progression
        startAutoProgress();

        // Pause auto-progression when user hovers over the section
        section.addEventListener('mouseenter', () => {
            stopAutoProgress();
        });

        section.addEventListener('mouseleave', () => {
            if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoProgress();
            } else if (isAutoProgressing) {
                startAutoProgress();
            }
        });

        // Handle window resize to recalculate line heights
        window.addEventListener('resize', () => {
            updateLineHeights();
        });
    });
});
