<?php

/**
 * Theme setup.
 */

namespace App;

use function Roots\bundle;

/**
 * Register the theme assets.
 *
 * @return void
 */
add_action('wp_enqueue_scripts', function () {
    bundle('app')->enqueue();
    bundle('button')->enqueue();
    bundle('accordion-init')->enqueue();
    bundle('atlanters-init')->enqueue();
    bundle('animation')->enqueue();
    bundle('blog-init')->enqueue();
    bundle('carousel-init')->enqueue();
    bundle('case-studies-collection-init')->enqueue();
    bundle('contact-form-init')->enqueue();
    bundle('counter-init')->enqueue();
    bundle('cta-init')->enqueue();
    bundle('dynamic-clamp')->enqueue();
    bundle('grid-init')->enqueue();
    bundle('hero-init')->enqueue();
    bundle('case-studies-init')->enqueue();
    bundle('case-study-technologies-init')->enqueue();
    bundle('nav')->enqueue();
    bundle('partnership-models-init')->enqueue();
    bundle('single')->enqueue();
    bundle('slider-text-init')->enqueue();
    bundle('slider-large-init')->enqueue();
    bundle('tabs-init')->enqueue();
    bundle('testimonials-init')->enqueue();
    bundle('interns-init')->enqueue();
    bundle('internship-benefits-init')->enqueue();
    bundle('newsletter-form-init')->enqueue();
}, 100);

/**
 * Register the theme assets with the block editor.
 *
 * @return void
 */
add_action('enqueue_block_editor_assets', function () {
    // Blocks
    $blocksDir = get_template_directory() . '/resources/scripts/blocks';
    $blockFiles = glob($blocksDir . '/*.js');

    foreach ($blockFiles as $blockFile) {
        $blockName = basename($blockFile, '.js');
        bundle($blockName)->enqueue();
    }

    // Other
    bundle('editor')->enqueue();
}, 100);

/**
 * Register the initial theme setup.
 *
 * @return void
 */
add_action('after_setup_theme', function () {
    /**
     * Disable full-site editing support.
     */
    remove_theme_support('block-templates');

    /**
     * Register the navigation menus.
     */
    register_nav_menus([
        'primary_navigation' => __('Primary Navigation', 'sage'),
    ]);

    /**
     * Disable the default block patterns.
     */
    remove_theme_support('core-block-patterns');

    /**
     * Enable plugins to manage the document title.
     */
    add_theme_support('title-tag');

    /**
     * Enable post thumbnail support.
     */
    add_theme_support('post-thumbnails');

    /**
     * Enable responsive embed support.
     */
    add_theme_support('responsive-embeds');

    /**
     * Enable HTML5 markup support.
     */
    add_theme_support('html5', [
        'caption',
        'comment-form',
        'comment-list',
        'gallery',
        'search-form',
        'script',
        'style',
    ]);

    /**
     * Enable selective refresh for widgets in customizer.
     */
    add_theme_support('customize-selective-refresh-widgets');
}, 20);

/**
 * Register the theme sidebars.
 *
 * @return void
 */
add_action('widgets_init', function () {
    $config = [
        'before_widget' => '<section class="widget %1$s %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3>',
        'after_title' => '</h3>',
    ];

    register_sidebar([
        'name' => __('Primary', 'sage'),
        'id' => 'sidebar-primary',
    ] + $config);

    register_sidebar([
        'name' => __('Footer', 'sage'),
        'id' => 'sidebar-footer',
    ] + $config);
});

add_action('admin_enqueue_scripts', function ($hook_suffix) {
    if ('nav-menus.php' !== $hook_suffix) {
        return;
    }

    wp_enqueue_media();
    bundle('menu-editor')->enqueue();
    bundle('menu-editor')->enqueue('css');
}, 100);

add_action('wp_enqueue_scripts', function () {
    bundle('custom')->enqueue('css');
}, 100);

require_once __DIR__ . '/blocks.php';
